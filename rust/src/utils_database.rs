use std::collections::HashMap;
use std::path::Path;
use std::process::Command;

use indexmap::IndexMap;
use mysql::{
    OptsBuilder,
    Pool,
    prelude::Queryable,
};

use crate::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
};

use crate::rahavard::{
    convert_byte,
    save_log,
    sort_dict,
};


pub fn get_size_of_database(database_name: &str, convert: bool) -> Result<String, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value().value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value().value_string()))
        .db_name(Some(database_name.clone()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let result: Option<u64> = conn.exec_first(query, (database_name,))?;
    let size_in_bytes = result.unwrap_or(0);

    if convert {
        Ok(convert_byte(size_in_bytes as f64))
    } else {
        Ok(size_in_bytes.to_string())
    }
}


pub fn get_size_of_table(database_name: &str, table_name: &str, convert: bool) -> Result<String, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value().value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value().value_string()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
        FROM INFORMATION_SCHEMA.TABLES
        WHERE (
            TABLE_SCHEMA = ? AND
            TABLE_NAME = ?
        )
    ";

    let result: Option<u64> = conn.exec_first(query, (database_name, table_name))?;
    let size_in_bytes = result.unwrap_or(0);

    if convert {
        Ok(convert_byte(size_in_bytes as f64))
    } else {
        Ok(size_in_bytes.to_string())
    }
}


pub fn get_tables(database_name: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value().value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value().value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value().value_string()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let table_names: Vec<String> = conn.exec(query, (database_name,))?;

    Ok(table_names)
}


pub fn get_tables_and_sizes(
    database_name: &str,
    sort: bool,
    based_on: &str,
    reverse: bool,
    convert: bool,
) -> Result<IndexMap<String, String>, Box<dyn std::error::Error>> {
    let mut dictionary: HashMap<String, u64> = HashMap::new();

    let tables = get_tables(database_name)?;

    for table in tables {
        // passing convert as False so we can:
        //   properly sort dictionary first, and
        //   later decide whether to convert size when returning
        let size__str = get_size_of_table(database_name, &table, false)?;
        let size__int: u64 = size__str.parse().unwrap_or(0);
        dictionary.insert(table, size__int);
    }

    let final_dict = if sort {
        sort_dict(&dictionary, based_on, reverse)
    } else {
        dictionary.into_iter().collect::<IndexMap<String, u64>>()
    };

    if convert {
        let result: IndexMap<String, String> = final_dict
            .into_iter()
            .map(|(table, size)| (table, convert_byte(size as f64)))
            .collect();
        Ok(result)
    } else {
        let result: IndexMap<String, String> = final_dict
            .into_iter()
            .map(|(table, size)| (table, size.to_string()))
            .collect();
        Ok(result)
    }
}


/// Optimized batch insertion of infiles into database
///
/// This function processes multiple infiles in a single MySQL transaction
/// for significantly better performance compared to individual file processing.
///
/// # Arguments
/// * `infile_paths` - Vector of infile paths to process
/// * `database_name` - Name of the target database
/// * `table_name` - Name of the target table
/// * `db_keys` - Database column keys for the INSERT statement
/// * `command` - Command name for logging
/// * `host_name` - Host name for logging
/// * `log_file` - Log file path for logging
///
/// # Returns
/// * `Result<(), Box<dyn std::error::Error>>` - Success or error
pub fn batch_insert_infiles(
    infile_paths: &[String],
    database_name: &str,
    table_name: &str,
    db_keys: &str,
    command: &str,
    host_name: &str,
    log_file: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    // Filter existing infiles
    let existing_infiles: Vec<&String> = infile_paths.iter()
        .filter(|path| Path::new(path).exists())
        .collect();

    if existing_infiles.is_empty() {
        save_log(command, host_name, log_file, "No infiles found to process", true)?;
        return Ok(());
    }

    // Create a single MySQL command with multiple statements for better performance
    let mut combined_statements = Vec::new();

    // Add performance optimization settings
    combined_statements.push("SET UNIQUE_CHECKS=0;".to_string());
    combined_statements.push("SET FOREIGN_KEY_CHECKS=0;".to_string());
    combined_statements.push("SET AUTOCOMMIT=0;".to_string());
    combined_statements.push("START TRANSACTION;".to_string());

    // Add all LOAD DATA statements
    for infile_path in &existing_infiles {
        let infile_statement = format!(
            r#"{} "{}"
            INTO TABLE {}
            FIELDS TERMINATED BY "{}"
            ENCLOSED BY '{}'
            LINES TERMINATED BY "\n"
            (ID,{});"#,
            MYSQLConfig::get_infile_statement(),
            infile_path,
            table_name,
            &MYSQLConfig::TERMINATED_BY.value().value_string(),
            &MYSQLConfig::ENCLOSED_BY.value().value_string(),
            db_keys,
        );
        combined_statements.push(infile_statement);
    }

    // Commit the transaction
    combined_statements.push("COMMIT;".to_string());
    combined_statements.push("SET AUTOCOMMIT=1;".to_string());

    // Join all statements with newlines
    let batch_sql = combined_statements.join("\n");

    // Execute the batch command with a single mysql process
    save_log(command, host_name, log_file, &format!("  executing batch insert for {} infiles", existing_infiles.len()), true)?;

    let infile_status = Command::new("mysql")
        .args([
            "--local-infile=1",
            "-h", &MYSQLConfig::MYSQL_HOST.value().value_string(),
            "-u", &MYSQLConfig::MYSQL_MASTER.value().value_string(),
            &format!("-p{}", MYSQLConfig::MYSQL_MASTER_PASSWD.value().value_string()),
            "-e", &batch_sql,
            database_name,
        ])
        .status()?;

    if infile_status.success() {
        save_log(command, host_name, log_file, &format!("  successfully inserted {} infiles", existing_infiles.len()), true)?;
    } else {
        return Err(format!("batch infile statement failed for {} files", existing_infiles.len()).into());
    }

    Ok(())
}
